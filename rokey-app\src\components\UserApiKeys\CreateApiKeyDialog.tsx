'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Key,
  AlertTriangle,
  Copy,
  Eye,
  EyeOff,
  Info
} from 'lucide-react';
import { toast } from 'sonner';

interface CreateApiKeyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateApiKey: (keyData: any) => Promise<any>;
  configName: string;
  creating: boolean;
  subscriptionTier: string;
}

export function CreateApiKeyDialog({
  open,
  onOpenChange,
  onCreateApiKey,
  configName,
  creating,
  subscriptionTier
}: CreateApiKeyDialogProps) {
  const [step, setStep] = useState<'form' | 'success'>('form');
  const [createdApiKey, setCreatedApiKey] = useState<any>(null);
  const [showFullKey, setShowFullKey] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState({
    key_name: '',
    expires_at: ''
  });



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.key_name.trim()) {
      toast.error('Please enter a name for your API key');
      return;
    }

    try {
      const result = await onCreateApiKey({
        key_name: formData.key_name.trim(),
        expires_at: formData.expires_at || undefined
      });
      
      setCreatedApiKey(result);
      setStep('success');
    } catch (error) {
      // Error is handled in the parent component
    }
  };



  const copyApiKey = async () => {
    if (createdApiKey?.api_key) {
      try {
        await navigator.clipboard.writeText(createdApiKey.api_key);
        toast.success('API key copied to clipboard');
      } catch (error) {
        toast.error('Failed to copy API key');
      }
    }
  };

  const handleClose = () => {
    setStep('form');
    setCreatedApiKey(null);
    setShowFullKey(false);
    setFormData({
      key_name: '',
      expires_at: ''
    });
    onOpenChange(false);
  };

  if (step === 'success' && createdApiKey) {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Key className="h-5 w-5 text-green-600" />
              API Key Created Successfully
            </DialogTitle>
            <DialogDescription>
              Your API key has been created. Make sure to copy it now as you won't be able to see it again.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <Alert className="border-amber-200 bg-amber-50">
              <AlertTriangle className="h-4 w-4 text-amber-600" />
              <AlertDescription className="text-amber-800">
                <strong>Important:</strong> This is the only time you'll see the full API key. 
                Make sure to copy and store it securely.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <Label>API Key</Label>
              <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border">
                <code className="flex-1 text-sm font-mono">
                  {showFullKey ? createdApiKey.api_key : `${createdApiKey.key_prefix}_${'*'.repeat(32)}`}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFullKey(!showFullKey)}
                  className="h-8 w-8 p-0"
                >
                  {showFullKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyApiKey}
                  className="h-8 w-8 p-0"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <Label>Key Name</Label>
                <p className="font-medium">{createdApiKey.key_name}</p>
              </div>
              <div>
                <Label>Created</Label>
                <p className="font-medium">{new Date(createdApiKey.created_at).toLocaleString()}</p>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Rate Limits</Label>
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center p-2 bg-blue-50 rounded">
                  <div className="font-semibold text-blue-900">{createdApiKey.rate_limits.per_minute}</div>
                  <div className="text-blue-700">per minute</div>
                </div>
                <div className="text-center p-2 bg-blue-50 rounded">
                  <div className="font-semibold text-blue-900">{createdApiKey.rate_limits.per_hour}</div>
                  <div className="text-blue-700">per hour</div>
                </div>
                <div className="text-center p-2 bg-blue-50 rounded">
                  <div className="font-semibold text-blue-900">{createdApiKey.rate_limits.per_day}</div>
                  <div className="text-blue-700">per day</div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={handleClose} className="w-full">
              Done
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Create API Key
          </DialogTitle>
          <DialogDescription>
            Create a new API key for programmatic access to {configName}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="key_name">API Key Name *</Label>
              <Input
                id="key_name"
                value={formData.key_name}
                onChange={(e) => setFormData(prev => ({ ...prev, key_name: e.target.value }))}
                placeholder="e.g., Production API Key"
                required
              />
              <p className="text-xs text-gray-600">
                A descriptive name to help you identify this API key
              </p>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="space-y-2">
                <h4 className="font-medium text-blue-900">Default Settings Applied</h4>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>• <strong>Permissions:</strong> Full access to chat, streaming, and all models in your configuration</p>
                  <p>• <strong>Rate Limits:</strong> Based on your {subscriptionTier} plan limits</p>
                  <p>• <strong>Security:</strong> No IP or domain restrictions (can be configured via API)</p>
                </div>
              </div>
            </div>
          </div>

          {/* Expiration */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="expires_at">Expiration Date (Optional)</Label>
              <Input
                id="expires_at"
                type="datetime-local"
                value={formData.expires_at}
                onChange={(e) => setFormData(prev => ({ ...prev, expires_at: e.target.value }))}
                min={new Date().toISOString().slice(0, 16)}
              />
              <p className="text-xs text-gray-600">
                Leave empty for no expiration
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating ? 'Creating...' : 'Create API Key'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
