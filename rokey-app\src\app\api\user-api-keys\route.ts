import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { Api<PERSON>eyGenerator } from '@/lib/userApiKeys/apiKeyGenerator';
import { z } from 'zod';
import type {
  CreateUserApiKeyRequest,
  GeneratedApiKeyResponse,
  UserGeneratedApiKey
} from '@/types/userApiKeys';

// Validation schema for creating API keys
const CreateApiKeySchema = z.object({
  custom_api_config_id: z.string().uuid(),
  key_name: z.string().min(1).max(100),
  permissions: z.object({
    chat: z.boolean().optional().default(true),
    streaming: z.boolean().optional().default(true),
    all_models: z.boolean().optional().default(true)
  }).optional(),
  rate_limit_per_minute: z.number().int().min(1).max(1000).optional(),
  rate_limit_per_hour: z.number().int().min(1).max(50000).optional(),
  rate_limit_per_day: z.number().int().min(1).max(500000).optional(),
  allowed_ips: z.array(z.string()).optional(),
  allowed_domains: z.array(z.string()).optional(),
  expires_at: z.string().datetime().optional()
});

// POST /api/user-api-keys - Generate a new API key
export async function POST(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  try {
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = CreateApiKeySchema.parse(body);

    // Check if user owns the custom API config
    const { data: config, error: configError } = await supabase
      .from('custom_api_configs')
      .select('id, name, user_id')
      .eq('id', validatedData.custom_api_config_id)
      .eq('user_id', user.id)
      .single();

    if (configError || !config) {
      return NextResponse.json({ 
        error: 'Custom API configuration not found or access denied' 
      }, { status: 404 });
    }

    // Get user's subscription tier
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('subscription_tier')
      .eq('id', user.id)
      .single();

    const subscriptionTier = profile?.subscription_tier || 'starter';

    // Check current API key count for this user
    const { count: currentKeyCount } = await supabase
      .from('user_generated_api_keys')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('status', 'active');

    // Validate subscription limits
    const limitCheck = ApiKeyGenerator.validateSubscriptionLimits(
      subscriptionTier,
      currentKeyCount || 0
    );

    if (!limitCheck.allowed) {
      return NextResponse.json({ 
        error: limitCheck.message 
      }, { status: 403 });
    }

    // Check for duplicate key name within the config
    const { data: existingKey } = await supabase
      .from('user_generated_api_keys')
      .select('id')
      .eq('custom_api_config_id', validatedData.custom_api_config_id)
      .eq('key_name', validatedData.key_name)
      .eq('status', 'active')
      .single();

    if (existingKey) {
      return NextResponse.json({ 
        error: 'An API key with this name already exists for this configuration' 
      }, { status: 409 });
    }

    // Generate the API key
    const { fullKey, prefix, secretPart, hash } = ApiKeyGenerator.generateApiKey();
    const encryptedSuffix = ApiKeyGenerator.encryptSuffix(secretPart);

    // Get default rate limits based on subscription tier
    const defaultRateLimits = ApiKeyGenerator.getDefaultRateLimits(subscriptionTier);

    // Prepare the API key data
    const apiKeyData = {
      user_id: user.id,
      custom_api_config_id: validatedData.custom_api_config_id,
      key_name: validatedData.key_name,
      key_prefix: prefix,
      key_hash: hash,
      encrypted_key_suffix: encryptedSuffix,
      permissions: validatedData.permissions || {
        chat: true,
        streaming: true,
        all_models: true
      },
      rate_limit_per_minute: validatedData.rate_limit_per_minute || defaultRateLimits.per_minute,
      rate_limit_per_hour: validatedData.rate_limit_per_hour || defaultRateLimits.per_hour,
      rate_limit_per_day: validatedData.rate_limit_per_day || defaultRateLimits.per_day,
      allowed_ips: validatedData.allowed_ips || [],
      allowed_domains: validatedData.allowed_domains || [],
      expires_at: validatedData.expires_at || null
    };

    // Insert the API key into the database
    const { data: createdKey, error: insertError } = await supabase
      .from('user_generated_api_keys')
      .insert(apiKeyData)
      .select()
      .single();

    if (insertError) {
      console.error('Error creating API key:', insertError);
      return NextResponse.json({ 
        error: 'Failed to create API key' 
      }, { status: 500 });
    }

    // Return the response with the full API key (only shown once)
    const response: GeneratedApiKeyResponse = {
      id: createdKey.id,
      key_name: createdKey.key_name,
      api_key: fullKey, // Full key - only shown once!
      key_prefix: createdKey.key_prefix,
      permissions: createdKey.permissions,
      rate_limits: {
        per_minute: createdKey.rate_limit_per_minute,
        per_hour: createdKey.rate_limit_per_hour,
        per_day: createdKey.rate_limit_per_day
      },
      created_at: createdKey.created_at,
      expires_at: createdKey.expires_at
    };

    return NextResponse.json(response, { status: 201 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid request data',
        details: error.errors 
      }, { status: 400 });
    }

    console.error('Error in POST /api/user-api-keys:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// GET /api/user-api-keys - List user's API keys
export async function GET(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  try {
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const configId = searchParams.get('config_id');

    // Build query
    let query = supabase
      .from('user_generated_api_keys')
      .select(`
        id,
        key_name,
        key_prefix,
        encrypted_key_suffix,
        permissions,
        rate_limit_per_minute,
        rate_limit_per_hour,
        rate_limit_per_day,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        status,
        expires_at,
        created_at,
        custom_api_configs!inner(
          id,
          name
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    // Filter by config if specified
    if (configId) {
      query = query.eq('custom_api_config_id', configId);
    }

    const { data: apiKeys, error } = await query;

    if (error) {
      console.error('Error fetching API keys:', error);
      return NextResponse.json({
        error: 'Failed to fetch API keys'
      }, { status: 500 });
    }

    // Transform the data to include masked keys
    const transformedKeys = apiKeys.map((key: any) => ({
      ...key,
      masked_key: ApiKeyGenerator.createMaskedKey(key.key_prefix, key.encrypted_key_suffix),
      // Remove sensitive data
      encrypted_key_suffix: undefined
    }));

    return NextResponse.json({ api_keys: transformedKeys });

  } catch (error) {
    console.error('Error in GET /api/user-api-keys:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
