import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { ApiKeyGenerator } from '@/lib/userApiKeys/apiKeyGenerator';
import { z } from 'zod';

interface RouteParams {
  params: Promise<{
    keyId: string;
  }>;
}

// Validation schema for updating API keys
const UpdateApiKeySchema = z.object({
  key_name: z.string().min(1).max(100).optional(),
  permissions: z.object({
    chat: z.boolean().optional(),
    streaming: z.boolean().optional(),
    all_models: z.boolean().optional()
  }).optional(),
  rate_limit_per_minute: z.number().int().min(1).max(1000).optional(),
  rate_limit_per_hour: z.number().int().min(1).max(50000).optional(),
  rate_limit_per_day: z.number().int().min(1).max(500000).optional(),
  allowed_ips: z.array(z.string()).optional(),
  allowed_domains: z.array(z.string()).optional(),
  status: z.enum(['active', 'inactive']).optional(),
  expires_at: z.string().datetime().nullable().optional()
});

// GET /api/user-api-keys/[keyId] - Get specific API key details
export async function GET(request: NextRequest, { params }: RouteParams) {
  const supabase = createSupabaseServerClientFromRequest(request);
  const { keyId } = await params;

  try {
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch the API key
    const { data: apiKey, error } = await supabase
      .from('user_generated_api_keys')
      .select(`
        id,
        key_name,
        key_prefix,
        encrypted_key_suffix,
        permissions,
        rate_limit_per_minute,
        rate_limit_per_hour,
        rate_limit_per_day,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        last_used_ip,
        status,
        expires_at,
        created_at,
        updated_at,
        custom_api_configs!inner(
          id,
          name
        )
      `)
      .eq('id', keyId)
      .eq('user_id', user.id)
      .single();

    if (error || !apiKey) {
      return NextResponse.json({ 
        error: 'API key not found' 
      }, { status: 404 });
    }

    // Transform the data
    const transformedKey = {
      ...apiKey,
      masked_key: ApiKeyGenerator.createMaskedKey(apiKey.key_prefix, apiKey.encrypted_key_suffix),
      // Remove sensitive data
      encrypted_key_suffix: undefined
    };

    return NextResponse.json({ api_key: transformedKey });

  } catch (error) {
    console.error('Error in GET /api/user-api-keys/[keyId]:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

// PATCH /api/user-api-keys/[keyId] - Update API key
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  const supabase = createSupabaseServerClientFromRequest(request);
  const { keyId } = await params;

  try {
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = UpdateApiKeySchema.parse(body);

    // Check if user owns this API key
    const { data: existingKey, error: fetchError } = await supabase
      .from('user_generated_api_keys')
      .select('id, user_id, custom_api_config_id, key_name')
      .eq('id', keyId)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !existingKey) {
      return NextResponse.json({ 
        error: 'API key not found' 
      }, { status: 404 });
    }

    // If updating key name, check for duplicates within the same config
    if (validatedData.key_name && validatedData.key_name !== existingKey.key_name) {
      const { data: duplicateKey } = await supabase
        .from('user_generated_api_keys')
        .select('id')
        .eq('custom_api_config_id', existingKey.custom_api_config_id)
        .eq('key_name', validatedData.key_name)
        .eq('status', 'active')
        .neq('id', keyId)
        .single();

      if (duplicateKey) {
        return NextResponse.json({ 
          error: 'An API key with this name already exists for this configuration' 
        }, { status: 409 });
      }
    }

    // Update the API key
    const { data: updatedKey, error: updateError } = await supabase
      .from('user_generated_api_keys')
      .update({
        ...validatedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', keyId)
      .eq('user_id', user.id)
      .select(`
        id,
        key_name,
        key_prefix,
        encrypted_key_suffix,
        permissions,
        rate_limit_per_minute,
        rate_limit_per_hour,
        rate_limit_per_day,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        status,
        expires_at,
        created_at,
        updated_at
      `)
      .single();

    if (updateError) {
      console.error('Error updating API key:', updateError);
      return NextResponse.json({ 
        error: 'Failed to update API key' 
      }, { status: 500 });
    }

    // Transform the response
    const transformedKey = {
      ...updatedKey,
      masked_key: ApiKeyGenerator.createMaskedKey(updatedKey.key_prefix, updatedKey.encrypted_key_suffix),
      // Remove sensitive data
      encrypted_key_suffix: undefined
    };

    return NextResponse.json({ api_key: transformedKey });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid request data',
        details: error.errors 
      }, { status: 400 });
    }

    console.error('Error in PATCH /api/user-api-keys/[keyId]:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

// DELETE /api/user-api-keys/[keyId] - Revoke API key
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  const supabase = createSupabaseServerClientFromRequest(request);
  const { keyId } = await params;

  try {
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user owns this API key
    const { data: existingKey, error: fetchError } = await supabase
      .from('user_generated_api_keys')
      .select('id, user_id')
      .eq('id', keyId)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !existingKey) {
      return NextResponse.json({ 
        error: 'API key not found' 
      }, { status: 404 });
    }

    // Mark the API key as revoked instead of deleting it
    const { error: updateError } = await supabase
      .from('user_generated_api_keys')
      .update({
        status: 'revoked',
        updated_at: new Date().toISOString()
      })
      .eq('id', keyId)
      .eq('user_id', user.id);

    if (updateError) {
      console.error('Error revoking API key:', updateError);
      return NextResponse.json({ 
        error: 'Failed to revoke API key' 
      }, { status: 500 });
    }

    return NextResponse.json({ 
      message: 'API key revoked successfully' 
    });

  } catch (error) {
    console.error('Error in DELETE /api/user-api-keys/[keyId]:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
