/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user-api-keys/route";
exports.ids = ["app/api/user-api-keys/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2Froute&page=%2Fapi%2Fuser-api-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2Froute&page=%2Fapi%2Fuser-api-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_user_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/user-api-keys/route.ts */ \"(rsc)/./src/app/api/user-api-keys/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user-api-keys/route\",\n        pathname: \"/api/user-api-keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/user-api-keys/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\user-api-keys\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_user_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2Froute&page=%2Fapi%2Fuser-api-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/user-api-keys/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/user-api-keys/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/userApiKeys/apiKeyGenerator */ \"(rsc)/./src/lib/userApiKeys/apiKeyGenerator.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\n// Validation schema for creating API keys\nconst CreateApiKeySchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    custom_api_config_id: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().uuid(),\n    key_name: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(1).max(100),\n    expires_at: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().datetime().optional()\n});\n// POST /api/user-api-keys - Generate a new API key\nasync function POST(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    try {\n        // Authenticate user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Parse and validate request body\n        const body = await request.json();\n        const validatedData = CreateApiKeySchema.parse(body);\n        // Check if user owns the custom API config\n        const { data: config, error: configError } = await supabase.from('custom_api_configs').select('id, name, user_id').eq('id', validatedData.custom_api_config_id).eq('user_id', user.id).single();\n        if (configError || !config) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Custom API configuration not found or access denied'\n            }, {\n                status: 404\n            });\n        }\n        // Get user's subscription tier from subscriptions table\n        const { data: subscription } = await supabase.from('subscriptions').select('tier').eq('user_id', user.id).eq('status', 'active').single();\n        const subscriptionTier = subscription?.tier || 'starter';\n        // Check current API key count for this user\n        const { count: currentKeyCount } = await supabase.from('user_generated_api_keys').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', user.id).eq('status', 'active');\n        // Validate subscription limits\n        const limitCheck = _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.validateSubscriptionLimits(subscriptionTier, currentKeyCount || 0);\n        if (!limitCheck.allowed) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: limitCheck.message\n            }, {\n                status: 403\n            });\n        }\n        // Check for duplicate key name within the config\n        const { data: existingKey } = await supabase.from('user_generated_api_keys').select('id').eq('custom_api_config_id', validatedData.custom_api_config_id).eq('key_name', validatedData.key_name).eq('status', 'active').single();\n        if (existingKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'An API key with this name already exists for this configuration'\n            }, {\n                status: 409\n            });\n        }\n        // Generate the API key\n        const { fullKey, prefix, secretPart, hash } = _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.generateApiKey();\n        const encryptedSuffix = _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.encryptSuffix(secretPart);\n        // Get default rate limits based on subscription tier\n        const defaultRateLimits = _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.getDefaultRateLimits(subscriptionTier);\n        // Prepare the API key data with default settings\n        const apiKeyData = {\n            user_id: user.id,\n            custom_api_config_id: validatedData.custom_api_config_id,\n            key_name: validatedData.key_name,\n            key_prefix: prefix,\n            key_hash: hash,\n            encrypted_key_suffix: encryptedSuffix,\n            permissions: {\n                chat: true,\n                streaming: true,\n                all_models: true\n            },\n            rate_limit_per_minute: defaultRateLimits.per_minute,\n            rate_limit_per_hour: defaultRateLimits.per_hour,\n            rate_limit_per_day: defaultRateLimits.per_day,\n            allowed_ips: [],\n            allowed_domains: [],\n            expires_at: validatedData.expires_at || null\n        };\n        // Insert the API key into the database\n        const { data: createdKey, error: insertError } = await supabase.from('user_generated_api_keys').insert(apiKeyData).select().single();\n        if (insertError) {\n            console.error('Error creating API key:', insertError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create API key'\n            }, {\n                status: 500\n            });\n        }\n        // Return the response with the full API key (only shown once)\n        const response = {\n            id: createdKey.id,\n            key_name: createdKey.key_name,\n            api_key: fullKey,\n            key_prefix: createdKey.key_prefix,\n            permissions: createdKey.permissions,\n            rate_limits: {\n                per_minute: createdKey.rate_limit_per_minute,\n                per_hour: createdKey.rate_limit_per_hour,\n                per_day: createdKey.rate_limit_per_day\n            },\n            created_at: createdKey.created_at,\n            expires_at: createdKey.expires_at\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request data',\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error('Error in POST /api/user-api-keys:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/user-api-keys - List user's API keys\nasync function GET(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    try {\n        // Authenticate user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get query parameters\n        const { searchParams } = new URL(request.url);\n        const configId = searchParams.get('config_id');\n        // Build query\n        let query = supabase.from('user_generated_api_keys').select(`\n        id,\n        key_name,\n        key_prefix,\n        encrypted_key_suffix,\n        permissions,\n        rate_limit_per_minute,\n        rate_limit_per_hour,\n        rate_limit_per_day,\n        allowed_ips,\n        allowed_domains,\n        total_requests,\n        last_used_at,\n        status,\n        expires_at,\n        created_at,\n        custom_api_configs!inner(\n          id,\n          name\n        )\n      `).eq('user_id', user.id).order('created_at', {\n            ascending: false\n        });\n        // Filter by config if specified\n        if (configId) {\n            query = query.eq('custom_api_config_id', configId);\n        }\n        const { data: apiKeys, error } = await query;\n        if (error) {\n            console.error('Error fetching API keys:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch API keys'\n            }, {\n                status: 500\n            });\n        }\n        // Transform the data to include masked keys\n        const transformedKeys = apiKeys.map((key)=>({\n                ...key,\n                masked_key: _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.createMaskedKey(key.key_prefix, key.encrypted_key_suffix),\n                // Remove sensitive data\n                encrypted_key_suffix: undefined\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            api_keys: transformedKeys\n        });\n    } catch (error) {\n        console.error('Error in GET /api/user-api-keys:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/user-api-keys/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/encryption.ts":
/*!*******************************!*\
  !*** ./src/lib/encryption.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ALGORITHM = 'aes-256-gcm';\nconst IV_LENGTH = 12; // Recommended for GCM\nconst AUTH_TAG_LENGTH = 16; // GCM produces a 16-byte auth tag\n// Ensure your ROKEY_ENCRYPTION_KEY is a 64-character hex string (32 bytes)\nconst ROKEY_ENCRYPTION_KEY_FROM_ENV = process.env.ROKEY_ENCRYPTION_KEY;\nconsole.log('[DEBUG] ROKEY_ENCRYPTION_KEY from process.env:', ROKEY_ENCRYPTION_KEY_FROM_ENV);\nconsole.log('[DEBUG] Length:', ROKEY_ENCRYPTION_KEY_FROM_ENV?.length);\nif (!ROKEY_ENCRYPTION_KEY_FROM_ENV || ROKEY_ENCRYPTION_KEY_FROM_ENV.length !== 64) {\n    throw new Error('Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.');\n}\nconst key = Buffer.from(ROKEY_ENCRYPTION_KEY_FROM_ENV, 'hex');\nfunction encrypt(text) {\n    if (typeof text !== 'string' || text.length === 0) {\n        throw new Error('Encryption input must be a non-empty string.');\n    }\n    const iv = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(IV_LENGTH);\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createCipheriv(ALGORITHM, key, iv);\n    let encrypted = cipher.update(text, 'utf8', 'hex');\n    encrypted += cipher.final('hex');\n    const authTag = cipher.getAuthTag();\n    // Prepend IV and authTag to the encrypted text (hex encoded)\n    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;\n}\nfunction decrypt(encryptedText) {\n    if (typeof encryptedText !== 'string' || encryptedText.length === 0) {\n        throw new Error('Decryption input must be a non-empty string.');\n    }\n    const parts = encryptedText.split(':');\n    if (parts.length !== 3) {\n        throw new Error('Invalid encrypted text format. Expected iv:authTag:encryptedData');\n    }\n    const iv = Buffer.from(parts[0], 'hex');\n    const authTag = Buffer.from(parts[1], 'hex');\n    const encryptedData = parts[2];\n    if (iv.length !== IV_LENGTH) {\n        throw new Error(`Invalid IV length. Expected ${IV_LENGTH} bytes.`);\n    }\n    if (authTag.length !== AUTH_TAG_LENGTH) {\n        throw new Error(`Invalid authTag length. Expected ${AUTH_TAG_LENGTH} bytes.`);\n    }\n    const decipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createDecipheriv(ALGORITHM, key, iv);\n    decipher.setAuthTag(authTag);\n    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');\n    decrypted += decipher.final('utf8');\n    return decrypted;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/encryption.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/userApiKeys/apiKeyGenerator.ts":
/*!************************************************!*\
  !*** ./src/lib/userApiKeys/apiKeyGenerator.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyGenerator: () => (/* binding */ ApiKeyGenerator)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/encryption */ \"(rsc)/./src/lib/encryption.ts\");\n\n\nclass ApiKeyGenerator {\n    static{\n        this.KEY_PREFIX = 'rk_live_';\n    }\n    static{\n        this.RANDOM_PART_LENGTH = 8 // hex chars for middle part\n        ;\n    }\n    static{\n        this.SECRET_PART_LENGTH = 32 // chars for secret part\n        ;\n    }\n    /**\n   * Generates a new API key with the format: rk_live_{8_hex_chars}_{32_random_chars}\n   * @returns Object containing the full key, prefix, and secret parts\n   */ static generateApiKey() {\n        // Generate random hex for the middle part (visible in prefix)\n        const randomHex = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(this.RANDOM_PART_LENGTH / 2).toString('hex');\n        // Generate random alphanumeric for the secret part\n        const secretPart = this.generateRandomString(this.SECRET_PART_LENGTH);\n        // Construct the full key\n        const prefix = `${this.KEY_PREFIX}${randomHex}`;\n        const fullKey = `${prefix}_${secretPart}`;\n        // Generate hash for storage\n        const hash = this.hashApiKey(fullKey);\n        return {\n            fullKey,\n            prefix,\n            secretPart,\n            hash\n        };\n    }\n    /**\n   * Generates a cryptographically secure random string\n   * @param length Length of the string to generate\n   * @returns Random alphanumeric string\n   */ static generateRandomString(length) {\n        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n        let result = '';\n        for(let i = 0; i < length; i++){\n            const randomIndex = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomInt(0, chars.length);\n            result += chars[randomIndex];\n        }\n        return result;\n    }\n    /**\n   * Creates a SHA-256 hash of the API key for secure storage\n   * @param apiKey The full API key to hash\n   * @returns SHA-256 hash as hex string\n   */ static hashApiKey(apiKey) {\n        return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha256').update(apiKey).digest('hex');\n    }\n    /**\n   * Validates the format of an API key\n   * @param apiKey The API key to validate\n   * @returns True if the format is valid\n   */ static isValidFormat(apiKey) {\n        const pattern = new RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`);\n        return pattern.test(apiKey);\n    }\n    /**\n   * Extracts the prefix from a full API key\n   * @param apiKey The full API key\n   * @returns The prefix part (e.g., \"rk_live_abc12345\")\n   */ static extractPrefix(apiKey) {\n        const parts = apiKey.split('_');\n        if (parts.length >= 3) {\n            return `${parts[0]}_${parts[1]}_${parts[2]}`;\n        }\n        return '';\n    }\n    /**\n   * Encrypts the suffix part of an API key for partial display\n   * @param secretPart The secret part of the API key\n   * @returns Encrypted suffix for storage\n   */ static encryptSuffix(secretPart) {\n        // Take last 4 characters for display purposes\n        const suffix = secretPart.slice(-4);\n        return (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_1__.encrypt)(suffix);\n    }\n    /**\n   * Decrypts the suffix for display\n   * @param encryptedSuffix The encrypted suffix from database\n   * @returns Decrypted suffix for display\n   */ static decryptSuffix(encryptedSuffix) {\n        try {\n            return (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(encryptedSuffix);\n        } catch (error) {\n            console.error('Failed to decrypt API key suffix:', error);\n            return '****';\n        }\n    }\n    /**\n   * Creates a masked version of the API key for display\n   * @param prefix The key prefix\n   * @param encryptedSuffix The encrypted suffix\n   * @returns Masked key for display (e.g., \"rk_live_abc12345_****xyz\")\n   */ static createMaskedKey(prefix, encryptedSuffix) {\n        const suffix = this.decryptSuffix(encryptedSuffix);\n        // Show 4 chars at the end, mask the rest (32 - 4 = 28 chars to mask)\n        const maskedLength = this.SECRET_PART_LENGTH - 4;\n        return `${prefix}_${'*'.repeat(maskedLength)}${suffix}`;\n    }\n    /**\n   * Validates subscription tier limits for API key generation\n   * @param subscriptionTier User's subscription tier\n   * @param currentKeyCount Current number of API keys for the user\n   * @returns Object indicating if generation is allowed and any limits\n   */ static validateSubscriptionLimits(subscriptionTier, currentKeyCount) {\n        const limits = {\n            starter: 2,\n            professional: 10,\n            enterprise: 50\n        };\n        const limit = limits[subscriptionTier] || limits.starter;\n        if (currentKeyCount >= limit) {\n            return {\n                allowed: false,\n                limit,\n                message: `You have reached the maximum number of API keys (${limit}) for your ${subscriptionTier} plan.`\n            };\n        }\n        return {\n            allowed: true,\n            limit\n        };\n    }\n    /**\n   * Gets default rate limits based on subscription tier\n   * @param subscriptionTier User's subscription tier\n   * @returns Default rate limits for the tier\n   */ static getDefaultRateLimits(subscriptionTier) {\n        const rateLimits = {\n            starter: {\n                per_minute: 30,\n                per_hour: 500,\n                per_day: 5000\n            },\n            professional: {\n                per_minute: 100,\n                per_hour: 2000,\n                per_day: 20000\n            },\n            enterprise: {\n                per_minute: 300,\n                per_hour: 10000,\n                per_day: 100000\n            }\n        };\n        return rateLimits[subscriptionTier] || rateLimits.starter;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/userApiKeys/apiKeyGenerator.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2Froute&page=%2Fapi%2Fuser-api-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();